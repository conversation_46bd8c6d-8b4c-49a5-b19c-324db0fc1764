import { useQuery } from '@tanstack/react-query';
import type { DataOverviewStats, StorageInfo, StoragePlan } from '../types';

/**
 * Query keys cho data overview
 */
export const DATA_OVERVIEW_QUERY_KEYS = {
  all: ['data', 'overview'] as const,
  stats: () => [...DATA_OVERVIEW_QUERY_KEYS.all, 'stats'] as const,
  storage: () => [...DATA_OVERVIEW_QUERY_KEYS.all, 'storage'] as const,
  plans: () => [...DATA_OVERVIEW_QUERY_KEYS.all, 'plans'] as const,
};

/**
 * Mock service để lấy data overview stats
 * TODO: Thay thế bằng API call thực tế
 */
const getDataOverviewStats = async (): Promise<DataOverviewStats> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return {
    totalMediaFiles: 1250,
    totalKnowledgeFiles: 340,
    totalUrls: 89,
    totalVectorStores: 12,
    storageUsed: '2.4 GB',
    lastUpdated: new Date().toISOString(),
  };
};

/**
 * Hook để lấy thống kê tổng quan data
 */
export const useDataOverview = () => {
  return useQuery({
    queryKey: DATA_OVERVIEW_QUERY_KEYS.stats(),
    queryFn: getDataOverviewStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

/**
 * Mock service để lấy storage information
 */
const getStorageInfo = async (): Promise<StorageInfo> => {
  await new Promise(resolve => setTimeout(resolve, 300));

  const used = 2.4;
  const total = 10;
  const percentage = Math.round((used / total) * 100);

  return {
    used,
    total,
    percentage,
    usedFormatted: `${used} GB`,
    totalFormatted: `${total} GB`,
    remainingFormatted: `${(total - used).toFixed(1)} GB`,
  };
};

/**
 * Mock service để lấy storage plans
 */
const getStoragePlans = async (): Promise<StoragePlan[]> => {
  await new Promise(resolve => setTimeout(resolve, 300));

  return [
    {
      id: 'basic',
      name: 'Gói Cơ Bản',
      storage: 10,
      price: 99000,
      priceFormatted: '99,000 ₫',
      features: ['10 GB lưu trữ', 'Hỗ trợ cơ bản', 'Upload không giới hạn'],
      isCurrentPlan: true,
    },
    {
      id: 'pro',
      name: 'Gói Pro',
      storage: 50,
      price: 299000,
      priceFormatted: '299,000 ₫',
      features: ['50 GB lưu trữ', 'Hỗ trợ ưu tiên', 'Backup tự động', 'API access'],
      isPopular: true,
    },
    {
      id: 'enterprise',
      name: 'Gói Enterprise',
      storage: 200,
      price: 999000,
      priceFormatted: '999,000 ₫',
      features: ['200 GB lưu trữ', 'Hỗ trợ 24/7', 'Backup tự động', 'API access', 'Custom integrations'],
    },
  ];
};

/**
 * Hook để lấy storage information
 */
export const useStorageInfo = () => {
  return useQuery({
    queryKey: DATA_OVERVIEW_QUERY_KEYS.storage(),
    queryFn: getStorageInfo,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook để lấy storage plans
 */
export const useStoragePlans = () => {
  return useQuery({
    queryKey: DATA_OVERVIEW_QUERY_KEYS.plans(),
    queryFn: getStoragePlans,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook để lấy data counts cho từng module
 */
export const useDataModuleCounts = () => {
  return useQuery({
    queryKey: [...DATA_OVERVIEW_QUERY_KEYS.all, 'module-counts'],
    queryFn: async () => {
      // TODO: Implement actual API calls
      return {
        media: 1250,
        knowledgeFiles: 340,
        urls: 89,
        vectorStores: 12,
      };
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
};
